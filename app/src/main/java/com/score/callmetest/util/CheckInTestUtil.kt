package com.score.callmetest.util

import com.score.callmetest.manager.CheckInManager
import timber.log.Timber

/**
 * 签到功能测试工具类
 * 用于测试和验证签到功能的正确性
 */
object CheckInTestUtil {
    
    private const val TAG = "CheckInTestUtil"
    
    /**
     * 测试签到功能
     */
    fun testCheckInFunction() {
        Timber.tag(TAG).d("开始测试签到功能")
        
        // 测试1：检查今天是否已签到
        val isCheckedToday = CheckInManager.isCheckedInToday()
        Timber.tag(TAG).d("今天是否已签到: $isCheckedToday")
        
        // 测试2：获取连续签到天数
        val continuousDays = CheckInManager.getContinuousCheckInDays()
        Timber.tag(TAG).d("连续签到天数: $continuousDays")
        
        // 测试3：获取总签到天数
        val totalDays = CheckInManager.getTotalCheckInDays()
        Timber.tag(TAG).d("总签到天数: $totalDays")
        
        // 测试4：获取签到奖励配置
        val rewards = CheckInManager.getCheckInRewards()
        Timber.tag(TAG).d("签到奖励配置: ${rewards.size}个奖励")
        rewards.forEachIndexed { index, reward ->
            Timber.tag(TAG).d("第${reward.day}天: ${reward.coins}金币, 特殊奖励: ${reward.isSpecial}")
        }
        
        Timber.tag(TAG).d("签到功能测试完成")
    }
    
    /**
     * 重置签到数据（仅用于测试）
     */
    fun resetCheckInData() {
        SharePreferenceUtil.remove("last_check_in_date")
        SharePreferenceUtil.remove("continuous_check_in_days")
        SharePreferenceUtil.remove("total_check_in_days")
        Timber.tag(TAG).d("签到数据已重置")
    }
}
