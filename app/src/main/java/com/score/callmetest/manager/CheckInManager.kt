package com.score.callmetest.manager

import android.app.Activity
import androidx.appcompat.app.AppCompatActivity
import com.score.callmetest.ui.widget.CheckInDialog
import com.score.callmetest.util.SharePreferenceUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.*

/**
 * 签到管理器
 * 统一管理签到功能
 */
object CheckInManager {
    
    private const val TAG = "CheckInManager"
    private const val KEY_LAST_CHECK_IN_DATE = "last_check_in_date"
    private const val KEY_CONTINUOUS_DAYS = "continuous_check_in_days"
    private const val KEY_TOTAL_CHECK_IN_DAYS = "total_check_in_days"
    
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    /**
     * 显示签到弹窗
     */
    fun showCheckInDialog(activity: AppCompatActivity) {
        try {
            val dialog = CheckInDialog()
            dialog.show(activity.supportFragmentManager, "check_in_dialog")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "显示签到弹窗失败")
        }
    }
    
    /**
     * 获取连续签到天数
     */
    fun getContinuousCheckInDays(): Int {
        return SharePreferenceUtil.getInt(KEY_CONTINUOUS_DAYS, 0)
    }
    
    /**
     * 获取总签到天数
     */
    fun getTotalCheckInDays(): Int {
        return SharePreferenceUtil.getInt(KEY_TOTAL_CHECK_IN_DAYS, 0)
    }
    
    /**
     * 检查今天是否已签到
     */
    fun isCheckedInToday(): Boolean {
        val lastCheckInDate = SharePreferenceUtil.getString(KEY_LAST_CHECK_IN_DATE, "")
        val today = dateFormat.format(Date())
        return lastCheckInDate == today
    }
    
    /**
     * 执行签到
     */
    fun checkIn(onSuccess: (() -> Unit)? = null, onError: ((String) -> Unit)? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                if (isCheckedInToday()) {
                    onError?.invoke("今天已经签到过了")
                    return@launch
                }
                
                val today = dateFormat.format(Date())
                val lastCheckInDate = SharePreferenceUtil.getString(KEY_LAST_CHECK_IN_DATE, "")?:""
                
                // 计算连续签到天数
                var continuousDays = getContinuousCheckInDays()
                if (isConsecutiveDay(lastCheckInDate, today)) {
                    continuousDays++
                } else {
                    continuousDays = 1
                }
                
                // 更新签到记录
                SharePreferenceUtil.putString(KEY_LAST_CHECK_IN_DATE, today)
                SharePreferenceUtil.putInt(KEY_CONTINUOUS_DAYS, continuousDays)
                SharePreferenceUtil.putInt(KEY_TOTAL_CHECK_IN_DAYS, getTotalCheckInDays() + 1)
                
                Timber.tag(TAG).d("签到成功，连续签到${continuousDays}天")
                
                // 在主线程回调成功
                CoroutineScope(Dispatchers.Main).launch {
                    onSuccess?.invoke()
                }
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "签到失败")
                CoroutineScope(Dispatchers.Main).launch {
                    onError?.invoke("签到失败：${e.message}")
                }
            }
        }
    }
    
    /**
     * 检查是否为连续的一天
     */
    private fun isConsecutiveDay(lastDate: String, currentDate: String): Boolean {
        if (lastDate.isEmpty()) return false
        
        try {
            val last = dateFormat.parse(lastDate)
            val current = dateFormat.parse(currentDate)
            
            if (last != null && current != null) {
                val calendar = Calendar.getInstance()
                calendar.time = last
                calendar.add(Calendar.DAY_OF_YEAR, 1)
                
                return dateFormat.format(calendar.time) == currentDate
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "日期解析失败")
        }
        
        return false
    }
    
    /**
     * 获取签到奖励配置
     * 返回7天的奖励配置
     */
    fun getCheckInRewards(): List<CheckInReward> {
        return listOf(
            CheckInReward(1, 60, false),
            CheckInReward(2, 60, false),
            CheckInReward(3, 60, false),
            CheckInReward(4, 60, false),
            CheckInReward(5, 60, false),
            CheckInReward(6, 60, false),
            CheckInReward(7, 2000, true) // 第7天是特殊奖励
        )
    }
    
    /**
     * 签到奖励数据类
     */
    data class CheckInReward(
        val day: Int,
        val coins: Int,
        val isSpecial: Boolean = false
    )
}
