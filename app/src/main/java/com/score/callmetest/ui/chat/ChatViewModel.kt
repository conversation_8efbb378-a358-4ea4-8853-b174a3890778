package com.score.callmetest.ui.chat

import android.net.Uri
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.entity.MessageType
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.ui.message.MessageIncomingManager
import io.rong.imlib.model.Message
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.UUID
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.SceneSource
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.entity.MessageEvents
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.im.callback.ImResultCallback
import com.score.callmetest.im.callback.ImSendMediaMsgCallback
import com.score.callmetest.im.callback.ImSendMsgCallback
import com.score.callmetest.im.download.DownloadPriority
import com.score.callmetest.im.download.HQAutoDownloadEntry
import com.score.callmetest.im.download.HQVoiceMsgDownloadManager
import com.score.callmetest.manager.AudioPlayManager
import com.score.callmetest.manager.AudioRecorderManager
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.ComplainInsertRecordRequest
import com.score.callmetest.network.FAQInfoList
import com.score.callmetest.network.FaqInfo
import com.score.callmetest.network.GetUserOnlineStatusPostV2Request
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.network.GiveGiftRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RemoveBlockRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.widget.BlockEvent
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.TimeUtils
import io.rong.common.FileUtils
import io.rong.message.HQVoiceMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import kotlin.collections.contains
import kotlin.math.min

/**
 * 聊天界面ViewModel
 */
class ChatViewModel : ViewModel() {

    companion object {
        private const val TAG = "ChatViewModel"
    }

    // 是否是客服
    private var mIsRobotService = true

    // 聊天对象信息
    private var targetUser: UserInfo? = null

    // 消息列表
    private val _messages = MutableLiveData<List<ChatMessageEntity>>(emptyList())
    val messages: LiveData<List<ChatMessageEntity>> = _messages

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val mIsLoading: LiveData<Boolean> = _isLoading

    // 发送状态
    private val _sendingStatus = MutableLiveData<SendStatus>()
    val sendingStatus: LiveData<SendStatus> = _sendingStatus

    // 在线状态
    private val _onlineStatus = MutableLiveData<String>()
    val onlineStatus: LiveData<String> = _onlineStatus
    private var mOnlineStatus = CallStatus.UNKNOWN

    private val handler = Handler(Looper.getMainLooper())

    // 录音状态
    private val _recordingState = MutableLiveData<RecordingState>()
    val recordingState: LiveData<RecordingState> = _recordingState
    
    // 录音时间文本
    private val _recordTimeText = MutableLiveData(0)
    val recordTimeText: LiveData<Int> = _recordTimeText
    
    // 最大录音时长（ms）
    private val maxRecordingDuration = 60_000L

    // 语音播放暂停
    private val _stopVoice = MutableLiveData<Boolean>()
    val stopVoice: LiveData<Boolean> = _stopVoice

    // 当前用户信息
    private val mCurrentUserId = UserInfoManager.myUserInfo?.userId?:""
    private val mCurrentUserName = UserInfoManager.myUserInfo?.nickname?:""
    private val mCurrentUserAvatar = UserInfoManager.myUserInfo?.avatarThumbUrl?:""

    // 是否开启自动翻译
    private val mIsAutoTrans: Boolean

    private var mCanLoadMore = true

    /**
     * 用于msg-list
     */
    private var mCurrentItemEntity: MessageListEntity? = null

    init {
        // 设置MessageIncomingManager的聊天消息回调
        MessageIncomingManager.setChatMessageCallback { message ->
            onReceiveMessage(message)
        }

        // 设置Call的聊天消息回调
        EventBus.observe(viewModelScope, MessageEvents.NewCallMessage::class.java){ event ->
            val chatMessage = event.chatMessage
            if((chatMessage.senderId == targetUser?.userId
                        && chatMessage.receiverId == mCurrentUserId) ||
                (chatMessage.senderId == mCurrentUserId
                        && chatMessage.receiverId == targetUser?.userId)) {
                // 直接收当前用户发送或者对方发送的消息
                onReceiveMessage(event.chatMessage)
                saveLastMessage(event.chatMessage)
            }
        }

        // 设置录音时间更新监听器
        AudioRecorderManager.setOnRecordingTimeUpdateListener { durationMs ->
            // 更新录音时间文本
            _recordTimeText.postValue((durationMs/1000).toInt())
        }

        // 设置录音错误监听器
        AudioRecorderManager.setOnRecordErrorListener { exception ->
            _recordingState.postValue(RecordingState.ERROR)
        }

        mIsAutoTrans = SharePreferenceUtil.getBoolean(
            Constant.AUTO_TRANSLATE,
            true,
            "settings"
        )
    }

    override fun onCleared() {
        super.onCleared()
        // 清理MessageIncomingManager的聊天消息回调
        MessageIncomingManager.setChatMessageCallback(null)
        
        // 释放音频资源
        AudioRecorderManager.release()

        // handler
        handler.removeCallbacksAndMessages(null)
    }

    /**
     * 初始化聊天界面
     * @param user 目标主播信息
     */
    fun initChat(user: UserInfo) {
        targetUser = user

        // 客服吗
        mIsRobotService = user.userId == Constant.ROBOt_ID

        if(!mIsRobotService) {
            // 查询在线状态
            checkOnlineStatus(user.userId)
            // 加载历史消息--客服首次不加载历史，因为没有历史
            _isLoading.value = true
            loadHistoryMessages(user.userId,null)
        }else {
            // 客服加载FAQ信息
            loadFaqs()
        }
    }

    // <editor-folder desc="加载faq-list">

    private var mFaqInfoResponse: FAQInfoList? = null

    private val mSysServiceName by lazy {
        CallmeApplication.context.getString(R.string.msg_item_service_name)
    }

    private fun loadFaqs() {
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getFaqList()
                }
                if (response is NetworkResult.Success) {
                    mFaqInfoResponse = response.data
                    // 拿到信息了
                    makeFaqChatMsgs()
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    /**
     * 制作常见问题聊天消息
     */
    private fun makeFaqChatMsgs() {
        if(mFaqInfoResponse == null) return
        val newMessages = _messages.value?.toMutableList() ?: mutableListOf()

        // 构建FAQ消息内容：content + 问题列表
        val content = StringBuilder(mFaqInfoResponse!!.content)
        mFaqInfoResponse!!.faqInfoList?.forEach { faqInfo ->
            content.append("\n\n").append(faqInfo.question)
        }

        // 将FAQ信息序列化为JSON存储在extra字段中
        val faqInfoJson = try {
            Gson().toJson(mFaqInfoResponse)
        } catch (e: Exception) {
            Timber.e(e, "Failed to serialize FAQ info")
            ""
        }

        // 创建FAQ消息
        val faqMessage = ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            currentUserId = UserInfoManager.myUserInfo?.userId ?: "",
            senderId = Constant.ROBOt_ID,
            senderName = mSysServiceName,
            senderAvatar = targetUser?.avatarThumbUrl ?: "",
            receiverId = mCurrentUserId,
            content = content.toString(),
            messageType = MessageType.ROBOT,
            status = MessageStatus.RECEIVED,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = false,
            extra = faqInfoJson
        )

        newMessages.add(faqMessage)
        _messages.postValue(newMessages)
    }

    /**
     * 处理FAQ问题点击事件
     * @param faqCode FAQ问题的code
     */
    fun handleFaqQuestionClick(faqCode: Int,handleType: (FaqInfo) -> Unit) {
        viewModelScope.launch {
            val faqInfo = mFaqInfoResponse?.faqInfoList?.find { it.code == faqCode }
            if (faqInfo != null) {
                Timber.d("FAQ question clicked: code=$faqCode, question=${faqInfo.question}")

                // 根据faqInfo.type判断是否需要发送问题文本消息
                if(faqInfo.type == 1) {
                    // 1. 用户发送问题文本消息
                    sendUserQuestionMessage(faqInfo.question)

                    // 2. 客服自动回复答案（如果有messageAnswer）
                    if (faqInfo.messageAnswer != null) {
                        delay(500L)
                        sendFaqAnswerMessage(faqInfo)
                    }
                }else {
                    ThreadUtils.runOnMain {
                        handleType(faqInfo)
                    }
                }
            } else {
                Timber.w("FAQ info not found for code: $faqCode")
            }
        }
    }

    /**
     * 发送用户问题消息
     * @param question 问题文本
     */
    private fun sendUserQuestionMessage(question: String) {
        // 过滤一下开头 *
        val questionText = question.trimStart('*')
        // 发送普通文本消息
        sendTextMessage(questionText.trim())
    }

    /**
     * 发送FAQ回答消息（延迟一点时间模拟客服回复）
     * @param faqInfo FAQ信息
     */
    private fun sendFaqAnswerMessage(faqInfo: FaqInfo) {
        val answerContent = faqInfo.messageAnswer?.content ?: return
        // 创建普通文本回答消息
        val answerMessage = ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            currentUserId = mCurrentUserId,
            senderId = Constant.ROBOt_ID,
            senderName = mSysServiceName,
            senderAvatar = targetUser?.avatarThumbUrl ?: "",
            receiverId = mCurrentUserId,
            content = answerContent,
            messageType = MessageType.ROBOT,
            status = MessageStatus.RECEIVED,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = false,
            extra = null
        )

        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        currentMessages.add(answerMessage)
        _messages.postValue(currentMessages)
    }

    // </editor-folder>

    // <editor-folder desc="更新在线状态">

    /**
     * 查询在线状态
     */
    fun checkOnlineStatus(userId: String?){
        if(userId.isNullOrEmpty()) return
        viewModelScope.launch {
            try {
                UserInfoManager.loadOnlineStatus(viewModelScope, userId){ status,_ ->
                    status?.let {
                        _onlineStatus.postValue(it)
                        updateStatus(it)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    fun updateStatus(status: String) {
        mOnlineStatus = status
    }

    // </editor-folder>

    private var loadResultCallback: ImResultCallback<List<Message>>? = null
    private var deleteResultCallback: ImResultCallback<Boolean>? = null

    /**
     * 加载历史消息
     * @param targetUserId 目标用户ID
     * @param oldestMessageId 最后一条消息的 ID。如需查询本地数据库中最新的消息，设置为 -1
     * @param count 数量--默认4
     */
    fun loadHistoryMessages(targetUserId: String?, oldestMessageId: String? = null, count: Int = 4 ) {
        Timber.d("loadHistoryMessages: $oldestMessageId")

        if(UserInfoManager.myUserInfo?.userId.isNullOrBlank() || targetUserId.isNullOrBlank()){
            _isLoading.value = false
            return
        }

        viewModelScope.launch {
            try {
                // 数据库查询
                val database = DatabaseFactory.getDatabase(CallmeApplication.context)
                // 获取
                val callback = object: (List<ChatMessageEntity>) -> Unit {
                    override fun invoke(list: List<ChatMessageEntity>) {
                        Timber.d("获取历史消息成功: ${list.size}")

                        if(list.isEmpty()){
                            // 没有更多了
                            mCanLoadMore = false
                            _isLoading.postValue(false)
                            return
                        }

                        if(list.size < count){
                            // 已经加载完了
                            mCanLoadMore = false
                        }

                        val chatMessages = list.map {
                            if(!it.isCurrentUser){
                                // 对方发送的消息
                                // 是否需要自动翻译这里设置一下，不看数据库的
                                it.isAutoTrans = mIsAutoTrans
                            }
                            return@map it
                        }

                        // 新查询出来的数据在前面，老数据在后面
                        val newMessages = if(_messages.value?.isEmpty() ?: true){
                            chatMessages.reversed()
                        }else {
                            chatMessages.reversed() + (_messages.value as List<ChatMessageEntity>)
                        }

                        _messages.postValue(newMessages)
                        // 这里不加postValue(false)，因为在viewModel.messages.observe有用到进行判断
//                        _isLoading.postValue(false)

                        // 只在首次load的时候更新
                        if(oldestMessageId == null && newMessages.isNotEmpty()) {
                            saveLastMessage(newMessages.last())
                        }
                    }
                }
                if(oldestMessageId.isNullOrBlank()){
                    // null---获取最新的
                    database.getLatestChatMessagesByUserId(mCurrentUserId,targetUserId, count,callback)
                }else {
                    // 不是null---分页
                    database.getChatMessagesBeforeMessageIdByUserId(mCurrentUserId,targetUserId, oldestMessageId, count,callback)
                }
            }catch (e: Exception) {
                Timber.e(e,"获取历史消息失败---")
                _isLoading.postValue(false)
            }
        }
    }

    fun checkLoadMore(): Boolean {
        return mCanLoadMore
    }

    fun loadUnreadCount(callback: (Int) -> Unit) {
        viewModelScope.launch {
            try {
                // 直接从数据库获取消息列表并计算未读数量
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .getCurrentUserAllMessageLists()
                    .catch { e ->
                        Timber.w("loadUnreadCount error: $e")
                    }
                    .collect { messageList ->
                        // 计算总未读数量
                        val totalUnreadCount = messageList.sumOf {
                            if(it.userId == targetUser?.userId){
                                // 当前用户不计算未读
                                return@sumOf 0
                            }
                            it.unreadCount
                        }
                        // 更新UI（需要在主线程执行）
                        callback(totalUnreadCount)
                        Timber.d("loadUnreadCount: $totalUnreadCount")
                    }
            } catch (e: Exception) {
                Timber.w("Failed to loadUnreadCount: $e")
            }
        }
    }

    /**
     * 检查充值链路是否有效
     * @param [extra] 邀请id的json
     * @param [callback] 回调
     */
    fun checkRechargeLinkValid(extra: String, callback: (Boolean,String) -> Unit){
        viewModelScope.launch {
            try {
                val jsonObj = JSONObject(extra)
                val invitationId = jsonObj.optString("invitationId")
                if(invitationId.isNullOrEmpty()){
                    // 预加载
                    GoodsManager.getBroadcasterInvitationGoods(invitationId)
                    callback(false,invitationId?:"")
                    return@launch
                }

                val response = RetrofitUtils.dataRepository.checkBroadcasterInvitation(invitationId)
                if(response is NetworkResult.Success){
                    // 有效
                    callback(true, invitationId)
                }else {
                    // 无效
                    callback(false, invitationId)
                }

            }catch (e: Exception){
                Timber.e(e)
                callback(false,"")
            }
        }
    }

    // <editor-folder desc="关注、拉黑、举报">

    // 关注 - 通过FollowManager
    fun follow(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.followUser(viewModelScope,userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                callback(true)
            }

            override fun onError(errorMsg: String) {
                callback(false)
            }
        })
    }

    // 取关 - 通过FollowManager
    fun unfollow(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.unfollowUser(viewModelScope,userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                callback(true)
            }

            override fun onError(errorMsg: String) {
                callback(false)
            }
        })
    }

    // 拉黑
    fun block(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.blockUser(
                        ComplainInsertRecordRequest(
                            broadcasterId = userId
                        )
                    )
                }
                if(resp is NetworkResult.Success){
                    EventBus.post(BlockEvent(userId, true))
                    RongCloudManager.addToBlackList(userId)
                    callback(true)
                }else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // 取消拉黑
    fun unblock(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.removeBlock(
                        RemoveBlockRequest(blockUserId = userId)
                    )
                }
                if (resp is NetworkResult.Success) {
                    EventBus.post(BlockEvent(userId, false))
                    RongCloudManager.removeFromBlackList(userId)
                    callback(true)
                } else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // 举报（带 complainSub）
    fun reportUser(userId: String, complainSub: String?, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.reportUser(
                        ComplainInsertRecordRequest(
                            broadcasterId = userId,
                            channelName = null,
                            complainSub = complainSub,
                            isAudit = false,
                            reason = null,
                            snapshotPath = null
                        )
                    )
                }
                callback(resp is NetworkResult.Success)
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // </editor-folder>

    // <editor-folder desc="发送消息相关">

    /**
     * 发送文本消息
     * @param content 消息内容
     */
    fun sendTextMessage(content: String,resendMessage: ChatMessageEntity? = null) {
        if (content.isBlank()) return
        val targetUserId = targetUser?.userId ?: return

        viewModelScope.launch {
            // 创建新消息
            val newMessage = resendMessage ?: ChatMessageEntity(
                messageId = UUID.randomUUID().toString(),
                currentUserId = mCurrentUserId,
                senderId = mCurrentUserId,
                senderName = mCurrentUserName,
                senderAvatar = mCurrentUserAvatar,
                receiverId = targetUserId,
                content = content,
                messageType = MessageType.TEXT,
                status = MessageStatus.SENDING,
                timestamp = System.currentTimeMillis(),
                isCurrentUser = true
            )

            val messageId = newMessage.messageId

            // 添加到消息列表
            val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
            if(resendMessage != null){
                // 重发--更新发送时间、状态
                val deleteMessage = currentMessages.find { msg ->
                    msg.messageId == newMessage.messageId
                }
                currentMessages.remove(deleteMessage)
                newMessage.apply {
                    this.status = MessageStatus.SENDING
                    this.timestamp = System.currentTimeMillis()
                }
            }
            currentMessages.add(newMessage)
            _messages.value = currentMessages

            // msg-list-save
            saveLastMessage(newMessage)

            // 客服不走融云
            if(mIsRobotService){
                updateMessageStatus(messageId, MessageStatus.SENT)
                _sendingStatus.postValue(SendStatus.Success(messageId))
                return@launch
            }

            withContext(Dispatchers.IO) {
                // 使用融云SDK发送消息
                RongCloudManager.sendTextMessage(
                    targetUserId,
                    content,
                    callback = object : ImSendMsgCallback() {

                        override fun success(message: Message?) {
                            // 消息发送成功
                            Timber.tag("RongCloud").d("消息发送成功--${message?.content}")
                            // 保存数据库
                            saveSentToDatabase(newMessage, true)

                            updateMessageStatus(messageId, MessageStatus.SENT)
                            _sendingStatus.postValue(SendStatus.Success(messageId))
                        }

                        override fun failed(code: Int?, errorMsg: String?) {
                            // 消息发送失败
                            Timber.tag("RongCloud").d("消息发送失败--$code: $errorMsg")
                            // 保存数据库
                            saveSentToDatabase(newMessage, false)

                            updateMessageStatus(messageId, MessageStatus.FAILED)
                            _sendingStatus.postValue(
                                SendStatus.Error(
                                    messageId,
                                    "发送失败: $errorMsg"
                                )
                            )

                        }
                    }
                )
            }
        }
    }

    /**
     * 发送图片消息
     * @param imageUri 图片URL
     */
    fun sendImageMessage(imageUri: Uri,resendMessage: ChatMessageEntity? = null) {
        val targetUserId = targetUser?.userId ?: return

        viewModelScope.launch {
            // 创建新消息
            val newMessage = resendMessage ?: ChatMessageEntity(
                messageId = UUID.randomUUID().toString(),
                currentUserId = mCurrentUserId,
                senderId = mCurrentUserId,
                senderName = mCurrentUserName,
                senderAvatar = mCurrentUserAvatar,
                receiverId = targetUserId,
                content = "",
                messageType = MessageType.IMAGE,
                status = MessageStatus.SENDING,
                timestamp = System.currentTimeMillis(),
                isCurrentUser = true,
                mediaLocalUri = imageUri
            )
            val messageId = newMessage.messageId


            // 添加到消息列表
            val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
            if(resendMessage != null){
                // 重发--更新发送时间、状态
                val deleteMessage = currentMessages.find { msg ->
                    msg.messageId == newMessage.messageId
                }
                currentMessages.remove(deleteMessage)
                newMessage.apply {
                    this.status = MessageStatus.SENDING
                    this.timestamp = System.currentTimeMillis()
                }
            }
            currentMessages.add(newMessage)
            _messages.value = currentMessages

            // msg-list-save
            saveLastMessage(newMessage)

            withContext(Dispatchers.IO){
                // 使用融云SDK发送图片消息
                try {
                    RongCloudManager.sendImageMessage(
                        targetUserId,
                        imageUri,
                        callback = object : ImSendMediaMsgCallback() {

                            override fun success(message: Message?) {
                                // 保存数据库
                                saveSentToDatabase(newMessage,true)

                                updateMessageStatus(messageId, MessageStatus.SENT)
                                _sendingStatus.postValue(SendStatus.Success(messageId))
                            }

                            override fun faild(code: Int?, errorMsg: String?) {
                                // 保存数据库
                                saveSentToDatabase(newMessage,false)

                                updateMessageStatus(messageId, MessageStatus.FAILED)
                                _sendingStatus.postValue(
                                    SendStatus.Error(
                                        messageId,
                                        "发送失败: $errorMsg"
                                    )
                                )
                            }

                            override fun onProgress(message: Message?, progress: Int) {
                                // 上传进度
                                Timber.tag("RongCloud").d("图片上传进度: $progress")
                            }
                        }
                    )
                } catch (e: Exception) {
                    // 保存数据库
                    saveSentToDatabase(newMessage,false)

                    Timber.tag("RongCloud").e("发送图片消息失败: ${e.message}")
                    updateMessageStatus(messageId, MessageStatus.FAILED)
                    _sendingStatus.postValue(SendStatus.Error(messageId, "发送失败: ${e.message}"))
                }
            }
        }
    }
    
    /**
     * 发送语音消息
     * @param duration 语音时长（ms）
     * @param audioUri 语音文件路径
     */
    fun sendVoiceMessage(duration: Long, audioUri: Uri?,resendMessage: ChatMessageEntity? = null) {
        val targetUserId = targetUser?.userId ?: return
        if(audioUri == null) return

        viewModelScope.launch {
            // 创建新消息
            val newMessage = resendMessage ?: ChatMessageEntity(
                messageId = UUID.randomUUID().toString(),
                currentUserId = mCurrentUserId,
                senderId = mCurrentUserId,
                senderName = mCurrentUserName,
                senderAvatar = mCurrentUserAvatar,
                receiverId = targetUserId,
                content = "",
                messageType = MessageType.VOICE,
                status = MessageStatus.SENDING,
                timestamp = System.currentTimeMillis(),
                isCurrentUser = true,
                mediaLocalUri = audioUri,
                mediaDuration = duration
            )
            val messageId = newMessage.messageId

            // 添加到消息列表
            val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
            if(resendMessage != null){
                // 重发--更新发送时间、状态
                val deleteMessage = currentMessages.find { msg ->
                    msg.messageId == newMessage.messageId
                }
                currentMessages.remove(deleteMessage)
                newMessage.apply {
                    this.status = MessageStatus.SENDING
                    this.timestamp = System.currentTimeMillis()
                }
            }
            currentMessages.add(newMessage)
            _messages.value = currentMessages

            // msg-list-save
            saveLastMessage(newMessage)

            withContext(Dispatchers.IO){
                // 使用融云SDK发送图片消息
                try {
                    RongCloudManager.sendVoiceMessage(
                        targetUserId,
                        audioUri,
                        (min(duration, maxRecordingDuration) /1000).toInt(),
                        callback = object : ImSendMediaMsgCallback() {

                            override fun success(message: Message?) {
                                // 保存数据库
                                saveSentToDatabase(newMessage,true)

                                updateMessageStatus(messageId, MessageStatus.SENT)
                                _sendingStatus.postValue(SendStatus.Success(messageId))
                            }

                            override fun faild(code: Int?, errorMsg: String?) {
                                // 保存数据库
                                saveSentToDatabase(newMessage,false)

                                updateMessageStatus(messageId, MessageStatus.FAILED)
                                _sendingStatus.postValue(
                                    SendStatus.Error(
                                        messageId,
                                        "发送失败: $errorMsg"
                                    )
                                )
                            }

                            override fun onProgress(message: Message?, progress: Int) {
                                // 上传进度
                                Timber.tag("RongCloud").d("语音上传进度: $progress")
                            }
                        }
                    )
                } catch (e: Exception) {
                    // 保存数据库
                    saveSentToDatabase(newMessage,false)

                    Timber.tag("RongCloud").e("发送语音消息失败: ${e.message}")
                    updateMessageStatus(messageId, MessageStatus.FAILED)
                    _sendingStatus.postValue(SendStatus.Error(messageId, "发送失败: ${e.message}"))
                }
            }
        }
    }

    /**
     * 送礼物
     * @param [giftInfo] 礼物信息
     * @param [resendMessage] 重新发送信息
     */
    fun sendGift(giftInfo: GiftInfo?,resendMessage: ChatMessageEntity? = null){
        Timber.d("receive send gift cmd---$giftInfo")
        if(giftInfo == null) return
        // 先确定targetUser是否还在
        val targetUserId = targetUser?.userId ?: return
        viewModelScope.launch {
            try {
                // 1. 中chat页面展示发送中。。。
                val newMessage = makeGiftMessageEntity(giftInfo,resendMessage)
                // 2. 请求后台发送礼物
                val response = withContext(Dispatchers.IO) {
                    val request = GiveGiftRequest(giftCode = giftInfo.code, num = 1, recipientUserId = targetUserId,
                        sceneSource = SceneSource.IM_GIFT_PANEL, giftSource = 0)
                    RetrofitUtils.dataRepository.giveUserGifts(request)
                }

                Timber.d("gift-sent-$response")
                if (response is NetworkResult.Success) {
                    // 赠送成功--发送融云gift消息并更新UI
                    // 调用接口成功了--->金币已经扣除，融云只是通知主播收到礼物，实际是收到了的
                    sendGiftMessage(giftInfo.code?: "",giftInfo, success = {}, failed = { _, errorMsg -> })
                    // 检查是否需要堆叠
                    if (!checkIsNeedOverlay(newMessage)) {
                        // 保存数据库
                        saveSentToDatabase(newMessage, true)
                        updateMessageStatus(newMessage.messageId, MessageStatus.SENT)
                        _sendingStatus.postValue(SendStatus.Success(newMessage.messageId))
                    }
                }else {
                    // 赠送失败-也标记失败
                    // 保存数据库
                    saveSentToDatabase(newMessage,false)

                    updateMessageStatus(newMessage.messageId, MessageStatus.FAILED)
                    _sendingStatus.postValue(SendStatus.Error(newMessage.messageId, "接口请求发送失败.."))
                }
                // msg-list-save
                saveLastMessage(newMessage)
            } catch (e: Exception) {
                Timber.e(e)
            }
        }

    }

    /**
     * make gift信息实体--用于展示
     *
     * 若10s内多次送出同一个礼物，gift信息需要合并，后面显示 x1、x2、x3...
     * 合并逻辑： 没有送出成功的，不合并；合并的以这里make时候的sentTime为主
     *
     * ps. 和UI确认，不需要x1,x2,x3...只需要x1。
     *
     * @param [giftInfo] 礼物信息
     */
    private fun makeGiftMessageEntity(giftInfo: GiftInfo,resendMessage: ChatMessageEntity? = null): ChatMessageEntity{
        // 创建新消息
        val newMessage = resendMessage ?: ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            currentUserId = mCurrentUserId,
            senderId = mCurrentUserId,
            senderName = mCurrentUserName,
            senderAvatar = mCurrentUserAvatar,
            receiverId = targetUser?.userId?:"",
            content = "",
            mediaDuration = 1, // 用来作为数量
            messageType = MessageType.GIFT,
            status = MessageStatus.SENDING,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = true,
            giftInfo = giftInfo
        )

        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        if(resendMessage != null){
            val deleteMessage = currentMessages.find { msg ->
                msg.messageId == newMessage.messageId
            }
            currentMessages.remove(deleteMessage)
            newMessage.apply {
                this.status = MessageStatus.SENDING
                this.timestamp = System.currentTimeMillis()
            }
        }
        currentMessages.add(newMessage)
        _messages.value = currentMessages

        return newMessage
    }

    /**
     * 检查是否需要叠加-->10s发送同一个礼物 消息为整合成一条
     *
     */
    private suspend fun checkIsNeedOverlay(newGiftMessage: ChatMessageEntity): Boolean = withContext(Dispatchers.IO) {
        // 0. 获取最新一条发送成功的消息
        val lastMessage = messages.value?.last {
            it.status == MessageStatus.SENT
        }
        if(lastMessage == null){
            // 1. 第一条消息
            return@withContext false
        }
        // 2. 检查上一条是否是礼物消息
        if(lastMessage.messageType != MessageType.GIFT){
            return@withContext false
        }
        // 3. 检查是不是同一个礼物
        if(lastMessage.giftInfo?.code.isNullOrBlank() ||
            lastMessage.giftInfo.code != newGiftMessage.giftInfo?.code){
            return@withContext false
        }
        // 4. 检查上一个礼物是否超过10秒
        if(newGiftMessage.timestamp - lastMessage.timestamp > 10 * 1000){
            return@withContext false
        }
        // ok--可以叠加--进行叠加操作
        val newItem = lastMessage.copy()
        newItem.timestamp = newGiftMessage.timestamp
        newItem.mediaDuration = lastMessage.mediaDuration + 1
        // 移除正在发送的礼物消息
        val newList = messages.value!!.toMutableList()
        newList.removeLastOrNull()
        // 覆盖叠加好的礼物消息至旧的
        newList[newList.lastIndex] = newItem
        // 更新数据库
        updateSentToDatabase(newItem)
        // 更新列表
        _messages.postValue(newList)

        return@withContext true
    }
    
    /**
     * 发送礼物消息
     * @param giftId 礼物ID
     * @param gift 礼物信息
     */
    private suspend fun sendGiftMessage(giftId: String,gift: GiftInfo, success:() -> Unit, failed: (Int?, String?) -> Unit) = withContext(Dispatchers.IO) {
        val targetUserId = targetUser?.userId ?: return@withContext

        // 使用融云SDK发送礼物消息
        try {
            RongCloudManager.sendGiftMessage(
                targetUserId,
                giftId,
                targetUser?.nickname?:"",
                mCurrentUserName,
                gift,
                callback = object : ImSendMsgCallback() {
                    override fun success(message: Message?) {
                        success()
                    }

                    override fun failed(code: Int?, errorMsg: String?) {
                        failed(code,errorMsg)
                    }
                }
            )
        } catch (e: Exception) {
            Timber.tag("RongCloud").e("发送gift消息失败: ${e.message}")
            failed(null,e.message)
        }
    }

    /**
     * 重新发送失败的消息
     * @param message 要重发的消息
     */
    fun resendMessage(message: ChatMessageEntity) {
        // 先删除这套消息，重新发送内容相同的新消息
        deleteLocalMessage(message.messageId){ result ->
            if(result){
                // 删除成功
                // 根据消息类型重新发送
                when (message.messageType) {
                    MessageType.TEXT -> {
                        sendTextMessage(message.content,message)
                    }
                    MessageType.IMAGE -> {
                        message.mediaLocalUri?.let { sendImageMessage(it,message) }
                    }
                    MessageType.VOICE -> {
                        sendVoiceMessage(message.mediaDuration, message.mediaLocalUri,message)
                    }
                    MessageType.GIFT -> {
                        sendGift(message.giftInfo,message)
                    }
                    else -> {
                        // 其他类型消息暂不处理
                        Timber.tag("RongCloud").e("不支持的消息类型: ${message.messageType}")
                        updateMessageStatus(message.messageId, MessageStatus.FAILED)
                        _sendingStatus.postValue(SendStatus.Error(message.messageId, "不支持的消息类型"))
                    }
                }
            }
        }
    }

    // </editor-folder>

    /**
     * 删除本地
     */
    private fun deleteLocalMessage(messageId: String,callback: (Boolean) -> Unit){
        try{
            DatabaseFactory.getDatabase(CallmeApplication.context)
                .deleteChatMessageById(messageId){ result ->
                    Timber.d("delete message ...$result")
                    callback.invoke(result)
                }
        }catch (e: Exception) {
            Timber.e(e)
        }
    }

    /**
     * 更新消息状态
     * @param messageId 消息ID
     * @param status 新状态
     */
    private fun updateMessageStatus(messageId: String, status: MessageStatus) {
        Timber.d("update message status...$messageId --$status")
        val currentMessages = _messages.value?.toMutableList()?: return
        val index = currentMessages.indexOfFirst { it.messageId == messageId }
        
        if (index != -1) {
            val updatedMessage = currentMessages[index].withStatus(status)
            currentMessages[index] = updatedMessage
            if(Looper.myLooper() == Looper.getMainLooper()) {
                // 主线程--直接set
                _messages.value = currentMessages
            }else {
                _messages.postValue(currentMessages)
            }
        }
    }

    /**
     * 发送信息数据库更新
     * @param [message] 消息
     * @param [isSent] 发送
     */
    private fun updateSentToDatabase(message: ChatMessageEntity) {
        try {
            // 使用新的，避免这里引用的message对象被修改
            DatabaseFactory.getDatabase(CallmeApplication.context)
                .updateChatMessage(message){ result ->
                    Timber.d("update message ...$result")
                }
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    /**
     * 发送信息保存到数据库
     * @param [message] 消息
     * @param [isSent] 发送
     */
    private fun saveSentToDatabase(message: ChatMessageEntity, isSent: Boolean) {
        try {
            val status = if(isSent) MessageStatus.SENT else MessageStatus.FAILED
            // 使用新的，避免这里引用的message对象被修改
            val newMsg = message.withStatus(status)
            DatabaseFactory.getDatabase(CallmeApplication.context)
                .insertChatMessage(newMsg){ result ->
                    Timber.d("insert message ...$result")
                }
        } catch (e: Exception) {
            Timber.e(e)
        }
    }

    
    /**
     * 消息发送状态
     */
    sealed class SendStatus {
        data class Success(val messageId: String) : SendStatus()
        data class Error(val messageId: String, val errorMessage: String) : SendStatus()
    }

    // <editor-folder desc="语音消息相关" >

    /**
     * 录音状态枚举
     */
    enum class RecordingState {
        INITIALIZED,    // 初始化完成
        RECORDING,      // 录音中
        COMPLETED,      // 录音完成
        COMPLETED_1S,   // 录音完成但小于1s
        CANCELED,       // 录音取消
        ERROR           // 录音错误
    }
    
    /**
     * 开始录音
     */
    fun startRecording() {
        // 设置录音状态为正在录音
        _recordingState.value = RecordingState.RECORDING
        
        // 使用AudioManager开始录音
        val success = AudioRecorderManager.startRecording(
            CallmeApplication.context,
            maxRecordingDuration
        ){
            // 达到最大录音时长了，不需要等用户触发stop，自行触发
            stopRecordingAndSend()
        }
        
        if (!success) {
            _recordingState.value = RecordingState.ERROR
        }
    }
    
    /**
     * 停止录音并发送
     */
    fun stopRecordingAndSend() {
        // 没有开始录音--stop也没意义
        if(_recordingState.value != RecordingState.RECORDING) return
        // 使用AudioManager停止录音
        val result = AudioRecorderManager.stopRecording()
        
        if (result == null) {
            _recordingState.value = RecordingState.ERROR
            return
        }
        
        val (fileUri, duration) = result
        
        // 检查录音时长
        if (duration < 1000L) {
            // 小于1s
            _recordingState.value = RecordingState.COMPLETED_1S
            return
        }
        
        // 设置录音状态为完成
        _recordingState.value = RecordingState.COMPLETED
        
        // 发送语音消息
        sendVoiceMessage(duration, fileUri)
    }
    
    /**
     * 取消录音
     */
    fun cancelRecording() {
        // 使用AudioManager取消录音
        AudioRecorderManager.cancelRecording()
        
        // 设置录音状态为取消
        _recordingState.value = RecordingState.CANCELED
    }
    
    /**
     * 获取录音剩余时间
     */
    fun getRecordingRemainingTime(): Long {
        return maxRecordingDuration - AudioRecorderManager.getCurrentRecordingDuration()
    }

    /**
     * 语音下载完成更新
     */
    fun updateAudioDownload(event: MessageEvents){
        viewModelScope.launch(Dispatchers.IO) {
            // 消息列表中查询这条语音并更新
            val msgId = (event as MessageEvents.AudioDownloadOk).msgId
            val localUri = event.localUri
            val newList = _messages.value?.map { ms ->
                if(ms.rcMsgId == msgId){
                    ms.copy(mediaLocalUri = localUri, isPlaying = false, isChange = true)
                } else ms
            }
            newList?.takeIf { it !== _messages.value }?.let { nonNullList ->
                _messages.postValue(nonNullList)
            }
        }
    }

    /**
     * 播放or 下载并播放 语音消息
     *
     * @param messageEntity 当前语音Item信息-自己
     *
     */
    fun handleVoiceClick(messageEntity: ChatMessageEntity) {
        viewModelScope.launch {
            // 处理暂停逻辑
            if (AudioPlayManager.isPlaying()) {
                val playingUri = AudioPlayManager.getPlayingUri()
                _stopVoice.value = true
                if(playingUri == messageEntity.mediaLocalUri){
                    // 暂停的是当前播放的 Uri
                    return@launch
                }
            }
            // 下载-播放处理
            playOrDownloadHQVoiceMsg(messageEntity)
        }
    }


    /**
     * 播放或下载
     *
     * @param [messageEntity] 消息实体
     */
    private fun playOrDownloadHQVoiceMsg(messageEntity: ChatMessageEntity){
        val uriValid = messageEntity.mediaLocalUri?.toString()?.isBlank()?:false
        val isNeedDownload = uriValid || !FileUtils.isFileExistsWithUri(CallmeApplication.context,messageEntity.mediaLocalUri)
        if(isNeedDownload) {
            // 手动download-->播放
            // 有自动下载了，这里进行兼容，一般不会进来这里
            // 先检查是否已经下载
            messageEntity.rcMsgId?.let {
                RongCloudManager.getMessageById(it.toInt(),object : ImResultCallback<Message>(){
                    override fun success(t: Message?) {
                        if(t == null) return
                        if(t.content !is HQVoiceMessage) return
                        val hqVoiceMessage = t.content as HQVoiceMessage
                        if(hqVoiceMessage.localPath?.toString()?.isNotBlank() == true){
                            // 已经下载了
                            val newMsg = messageEntity.copy(mediaLocalUri = hqVoiceMessage.localPath, isPlaying = false, isChange = true)
                            updateSentToDatabase(newMsg)
                            playVoiceMessage( newMsg)
                            return
                        }
                        // 高优先级入队下载
                        HQVoiceMsgDownloadManager.enqueue(HQAutoDownloadEntry(t, DownloadPriority.HIGH))
                    }

                    override fun error(code: Int?, errorMsg: String?) {
                        Timber.w("downloadMediaMessage error:$code, $errorMsg")
                    }
                })
            }

        }else {
            // 播放
            playVoiceMessage(messageEntity)
        }
    }

    /**
     * 播放语音
     * @param [messageEntity] 消息实体
     */
    private fun playVoiceMessage(messageEntity: ChatMessageEntity){
        AudioPlayManager.startPlay(
            messageEntity.mediaLocalUri,
            object: AudioPlayManager.IAudioPlayListener{
                override fun onStart(uri: Uri?) {
                    messageEntity.isPlaying = true
                    refreshSingleMessage(messageEntity)
                }

                override fun onStop(uri: Uri?) {
                    messageEntity.isPlaying = false
                    refreshSingleMessage(messageEntity)
                }

                override fun onComplete(uri: Uri?) {
                    messageEntity.isPlaying = false
                    refreshSingleMessage(messageEntity)
                    // todo-dsc 继续找下一个语音消息播放--1.0版本不做
                }
            }
        )
    }

    /**
     * 刷新单个消息
     * @param [messageEntity] 消息实体
     */
    private fun refreshSingleMessage(messageEntity: ChatMessageEntity) {
        val newList = _messages.value?.map { ms ->
            if(ms.messageId == messageEntity.messageId){
                if(ms !== messageEntity){
                    messageEntity.isChange = true
                    return@map messageEntity
                }
                ms.copy(isChange = true)
            } else ms
        }
        newList?.takeIf { it !== _messages.value }?.let { nonNullList ->
            _messages.postValue(nonNullList)
        }
    }


    // </editor-folder>
    
    /**
     * 处理从MessageIncomingManager接收到的聊天消息
     * 替代原来的融云消息监听
     */
    private fun onReceiveMessage(message: ChatMessageEntity) {
        Timber.tag(TAG).d("Received chat message from MessageIncomingManager: ${message.senderId}")

        message.isAutoTrans = mIsAutoTrans
        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        currentMessages.add(message)
        _messages.postValue(currentMessages)
    }

    /**
     * 保存最新一条--供message-list使用,发送成功or失败暂时不管
     */
    fun saveLastMessage(msg: ChatMessageEntity?){
        viewModelScope.launch {
            if (msg == null) return@launch
            if (mIsRobotService) return@launch

            val target = targetUser
            val isOfficialUser = StrategyManager.isTopOfficialUser(target?.userId ?: "")
            // 先查数据库有没有此人-有则更新-无则新增
            if (mCurrentItemEntity == null) {
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .getMessageListById(target?.userId ?: "") { entity ->
                        if (entity == null) {
                            // 数据库没有--新增
                            mCurrentItemEntity = MessageListEntity(
                                userId = target?.userId ?: "",
                                currentUserId = UserInfoManager.myUserInfo?.userId ?: "",
                                userName = target?.nickname ?: "",
                                gender = target?.gender ?: 1,
                                unitPrice = target?.unitPrice ?: 0,
                                avatar = target?.avatar ?: "",
                                avatarThumbUrl = target?.avatarThumbUrl ?: "",
                                lastMessage = msg.content,
                                lastMessageType = msg.messageType,
                                timestamp = TimeUtils.formatTimestampForChatList(msg.timestamp),
                                timeInMillis = msg.timestamp,
                                unreadCount = 0, // 在这里都是已读
                                onlineStatus = if (isOfficialUser) CallStatus.UNKNOWN else mOnlineStatus
                                    ?: CallStatus.UNKNOWN,
                                isPinned = isOfficialUser
                            )
                            DatabaseFactory.getDatabase(CallmeApplication.context)
                                .insertMessageList(mCurrentItemEntity!!)

                            return@getMessageListById
                        }
                        // 数据库有--更新
                        mCurrentItemEntity = entity
                        mCurrentItemEntity?.apply {
                            lastMessage =
                                if (msg.messageType == MessageType.GIFT) "" else msg.content
                            lastMessageType = msg.messageType
                            timestamp = TimeUtils.formatTimestampForChatList(msg.timestamp)
                            timeInMillis = msg.timestamp
                            unreadCount = 0 // 在这里都是已读
                            onlineStatus =
                                if (isOfficialUser) CallStatus.UNKNOWN else mOnlineStatus
                                    ?: CallStatus.UNKNOWN
                            // 只有官方号手动强制置顶
                            if (isOfficialUser) {
                                isPinned = true
                            }
                        }
                        // 更新数据库
                        DatabaseFactory.getDatabase(CallmeApplication.context)
                            .updateCurrentUserMessageList(mCurrentItemEntity!!)
                    }
            } else {
                // update
                mCurrentItemEntity?.apply {
                    lastMessage = if (msg.messageType == MessageType.GIFT) "" else msg.content
                    lastMessageType = msg.messageType
                    timestamp = TimeUtils.formatTimestampForChatList(msg.timestamp)
                    timeInMillis = msg.timestamp
                    unreadCount = 0 // 在这里都是已读
                    onlineStatus = if (isOfficialUser) CallStatus.UNKNOWN else mOnlineStatus
                        ?: CallStatus.UNKNOWN

                    // 只有官方号手动强制置顶
                    if (isOfficialUser) {
                        isPinned = true
                    }
                }
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .updateCurrentUserMessageList(mCurrentItemEntity!!)
            }
        }
    }

    fun clear() {
        loadResultCallback = null
        deleteResultCallback = null
    }
} 