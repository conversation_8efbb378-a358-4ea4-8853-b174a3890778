package com.score.callmetest.ui.mine.follow.adapter

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemFragmentFollowersBinding
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.FollowModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.mine.follow.BottomState
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class FollowersAdapter(
    private val onFollowClick: (FollowModel, Int) -> Unit,
) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_FOLLOWER = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }

    private val followers = mutableListOf<FollowModel>()
    private var bottomState: BottomState = BottomState.HIDDEN
    // 本地UI状态管理（仅用于显示，不修改原始数据）
    private val uiFollowStates = mutableMapOf<String, Boolean>()

    fun setBottomState(state: BottomState) {
        if (this.bottomState != state) {
            this.bottomState = state
            notifyDataSetChanged()
        }
    }

    fun setData(newFollowers: List<FollowModel>) {
        followers.clear()
        followers.addAll(newFollowers)

        // 初始化UI状态，使用原始数据
        uiFollowStates.clear()
        newFollowers.forEach { followModel ->
            uiFollowStates[followModel.userId] = followModel.isFollows ?: false
        }

        notifyDataSetChanged()
    }

    /**
     * 更新单个项目的UI显示状态
     */
    fun updateItemFollowState(position: Int, newState: Boolean) {
        if (position in 0 until followers.size) {
            val userId = followers[position].userId
            uiFollowStates[userId] = newState
            notifyItemChanged(position)
        }
    }


    override fun getItemCount(): Int = followers.size + if (bottomState != BottomState.HIDDEN) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (bottomState != BottomState.HIDDEN && position == followers.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_FOLLOWER
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_FOLLOWER -> {
                val binding = ItemFragmentFollowersBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ViewHolder(binding)
            }
            VIEW_TYPE_BOTTOM -> {
                val binding = ItemListBottomBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                BottomViewHolder(binding)
            }
            // todo 异常类型主动报错，后面可以删除
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder && position < followers.size) {
            val followModel = followers[position]
            holder.bind(followModel, position)
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }

    // 跳转到主播详情页面
    private fun ToBroadcasterDetailActivity(context: Context,position: Int) {
        if (position != RecyclerView.NO_POSITION && position < followers.size) {
            val followModel = followers[position]
            val broadcasterModel = followModelToBroadcasterModel(followModel)
            val intent = Intent(context, BroadcasterDetailActivity::class.java)
            intent.putExtra(Constant.BROADCASTER_MODEL, broadcasterModel)
            context.startActivity(intent)
        }
    }

    /**
     * 更新关注按钮UI状态
     */
    private fun updateFollowButtonUI(
        button: TextView,
        isFollowing: Boolean,
        cornerRadius: Float,
    ) {
        if (isFollowing) {
            button.text = "Following"
            val fillColor = ContextCompat.getColor(context, R.color.following_button_fill)
            button.background = DrawableUtils.createRoundRectDrawableDp(fillColor, cornerRadius)
        } else {
            button.text = "+ Follow"
            button.setTextColor(ContextCompat.getColor(context, R.color.follow_text_color))
            button.background = DrawableUtils.createRoundRectDrawableDp(
                ContextCompat.getColor(context, R.color.follow_button_fill),
                cornerRadius,
            )
        }
    }


    private fun followModelToBroadcasterModel(followModel: FollowModel): BroadcasterModel {
        return BroadcasterModel(
            userId = followModel.userId,
            nickname = followModel.nickname,
            avatar = followModel.avatar,
            gender = followModel.gender,
            age = followModel.age,
            country = followModel.country,
            status = followModel.onlineStatus ?: CallStatus.OFFLINE,
            callCoins = followModel.unitPrice,
            unit = "min", // 通话单位，默认"min"
            isFriend = followModel.isFollows,
            about = followModel.about,
            grade = followModel.level,
            analysisLanguage = followModel.language,
            isSignBroadcaster = followModel.isSignBroadcaster,
            showRoomVersion = followModel.showRoomVersion,
            broadcasterType = followModel.userType,
            avatarThumbUrl = followModel.avatarThumbUrl,
            isVip = followModel.isVip ?: false,
        )
    }

    inner class ViewHolder(private val binding: ItemFragmentFollowersBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(followModel: FollowModel, position: Int) {
            // 昵称
            binding.tvUsername.text = followModel.nickname ?: ""

            // 国家/地区
            binding.tvRegion.text = followModel.country ?: ""
            binding.ivFlag.setImageResource(CountryUtils.getIconByEnName(followModel.country))

            // 头像
            val avatarUrl = followModel.avatarUrl ?: followModel.avatarThumbUrl ?: ""
            if (avatarUrl.isNotEmpty()) {
                Glide.with(binding.ivAvatar.context)
                    .load(avatarUrl)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .into(binding.ivAvatar)
            } else {
                binding.ivAvatar.setImageResource(R.drawable.placeholder)
            }

            // 关注按钮状态 - 使用UI状态管理
            val isFollowing =  uiFollowStates[followModel.userId] ?: (followModel.isFollows ?: false)
            updateFollowButtonUI(binding.tvAddFollow, isFollowing, 14f)

            // 头像点击事件
            binding.ivAvatar.click {
                UserInfoManager.putCachedDrawable(
                    followModel.userId,
                    binding.ivAvatar.drawable
                )
                ToBroadcasterDetailActivity(itemView.context, adapterPosition)
            }

            // item点击事件
            itemView.click {
                UserInfoManager.putCachedDrawable(
                    followModel.userId,
                    binding.ivAvatar.drawable
                )
                ToBroadcasterDetailActivity(itemView.context, adapterPosition)
            }

            // 关注按钮点击事件
            binding.tvAddFollow.click {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION && position < followers.size) {
                    val followModel = followers[position]

                    // 立即更新本地状态和UI
                    val currentState = uiFollowStates[followModel.userId] ?: (followModel.isFollows ?: false)
                    val newState = !currentState
                    // 更新UI状态映射
                    uiFollowStates[followModel.userId] = newState

                    // 立即更新UI
                    updateFollowButtonUI(binding.tvAddFollow, newState, 14f)

                    // 调用回调处理网络请求
                    onFollowClick(followModel, position)
                }
            }
        }
    }

    inner class BottomViewHolder(private val binding: ItemListBottomBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(text: String) {
            when (bottomState) {
                BottomState.LOADING -> {
                    // 显示加载动画，隐藏静态底部
                    binding.layoutLoading.visibility = android.view.View.VISIBLE
                    binding.layoutFinished.visibility = android.view.View.GONE
                }
                BottomState.FINISHED -> {
                    // 隐藏加载动画，显示静态底部
                    binding.layoutLoading.visibility = android.view.View.GONE
                    binding.layoutFinished.visibility = android.view.View.VISIBLE
                    binding.tvBottomText.text = text
                }
                BottomState.HIDDEN -> {
                    // 隐藏所有（这种情况下不应该调用bind，但为了安全起见）
                    binding.layoutLoading.visibility = android.view.View.GONE
                    binding.layoutFinished.visibility = android.view.View.GONE
                }
            }
        }
    }
} 