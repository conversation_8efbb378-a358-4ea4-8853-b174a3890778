package com.score.callmetest.ui.widget

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.DialogFragment
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogCheckInBinding
import com.score.callmetest.manager.CheckInManager
import com.score.callmetest.util.ClickUtils
import timber.log.Timber

/**
 * 签到弹窗
 */
class CheckInDialog : DialogFragment() {

    private var _binding: DialogCheckInBinding? = null
    private val binding get() = _binding!!

    private val dayLayouts = mutableListOf<LinearLayout>()
    private val dayChecks = mutableListOf<ImageView>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogCheckInBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 设置透明背景
        dialog?.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        
        initViews()
        initListeners()
        updateUI()
    }

    private fun initViews() {
        // 收集所有天数的布局和勾选图标
        dayLayouts.apply {
            add(binding.day1Layout)
            add(binding.day2Layout)
            add(binding.day3Layout)
            add(binding.day4Layout)
            add(binding.day5Layout)
            add(binding.day6Layout)
            add(binding.day7Layout)
        }

        dayChecks.apply {
            add(binding.day1Check)
            add(binding.day2Check)
            add(binding.day3Check)
            add(binding.day4Check)
            add(binding.day5Check)
            add(binding.day6Check)
            add(binding.day7Check)
        }
    }

    private fun initListeners() {
        // 关闭按钮
        ClickUtils.setOnIsolatedClickListener(binding.btnClose) {
            dismiss()
        }

        // 签到按钮
        ClickUtils.setOnIsolatedClickListener(binding.btnCheckIn) {
            performCheckIn()
        }

        // VIP特权按钮
        ClickUtils.setOnIsolatedClickListener(binding.vipPrivilegeLayout) {
            onVipPrivilegeClicked()
        }
    }

    private fun updateUI() {
        val continuousDays = CheckInManager.getContinuousCheckInDays()
        val isCheckedToday = CheckInManager.isCheckedInToday()

        // 更新已签到的天数显示
        for (i in 0 until continuousDays.coerceAtMost(7)) {
            showDayAsChecked(i)
        }

        // 更新签到按钮状态
        if (isCheckedToday) {
            binding.btnCheckIn.visibility = View.GONE
            binding.tvCheckInTip.visibility = View.VISIBLE
            binding.tvCheckInTip.text = if (continuousDays >= 7) {
                "Congratulations! You've completed 7 days check-in!"
            } else {
                "See you tomorrow"
            }
        } else {
            binding.btnCheckIn.visibility = View.VISIBLE
            binding.tvCheckInTip.visibility = View.GONE
        }

        Timber.d("CheckInDialog: 更新UI - 连续签到${continuousDays}天, 今日已签到:${isCheckedToday}")
    }

    private fun showDayAsChecked(dayIndex: Int) {
        if (dayIndex < dayChecks.size) {
            dayChecks[dayIndex].apply {
                visibility = View.VISIBLE
                setImageResource(getCheckedIconResource())
            }
            
            // 为已签到的天数添加特殊样式
            dayLayouts[dayIndex].alpha = 1.0f
        }
    }

    private fun getCheckedIconResource(): Int {
        // 这里可以根据需要返回不同的勾选图标
        // 暂时使用项目中已有的图标
        return R.drawable.clean // 可以后续替换为更合适的勾选图标
    }

    private fun performCheckIn() {
        if (CheckInManager.isCheckedInToday()) {
            Toast.makeText(context, "今天已经签到过了", Toast.LENGTH_SHORT).show()
            return
        }

        CheckInManager.checkIn(
            onSuccess = {
                Toast.makeText(context, "签到成功！", Toast.LENGTH_SHORT).show()
                updateUI()
                
                // 触发签到成功事件
                onCheckInSuccess()
            },
            onError = { error ->
                Toast.makeText(context, error, Toast.LENGTH_SHORT).show()
            }
        )
    }

    /**
     * 签到成功回调
     * 可以在这里处理签到奖励等逻辑
     */
    private fun onCheckInSuccess() {
        val continuousDays = CheckInManager.getContinuousCheckInDays()
        val rewards = CheckInManager.getCheckInRewards()
        
        if (continuousDays <= rewards.size) {
            val todayReward = rewards[continuousDays - 1]
            Timber.d("CheckInDialog: 获得签到奖励 - ${todayReward.coins}金币")
            
            // 这里可以调用用户信息更新接口
            // UserInfoManager.addCoins(todayReward.coins)
        }
    }

    /**
     * VIP特权点击事件
     * 预留接口，可以跳转到VIP购买页面
     */
    private fun onVipPrivilegeClicked() {
        Timber.d("CheckInDialog: 点击VIP特权按钮")
        
        // 这里可以添加跳转到VIP页面的逻辑
        // 例如：启动VIP购买Activity或弹窗
        Toast.makeText(context, "VIP功能开发中...", Toast.LENGTH_SHORT).show()
        
        // 示例：
        // startActivity(Intent(context, VipActivity::class.java))
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onStart() {
        super.onStart()
        
        // 设置弹窗大小
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }
}
