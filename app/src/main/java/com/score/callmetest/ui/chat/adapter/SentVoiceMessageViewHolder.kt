package com.score.callmetest.ui.chat.adapter

import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemChatMessageVoiceRightBinding
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.GlideUtils

/**
 * 发送语音消息ViewHolder
 */
internal class SentVoiceMessageViewHolder(
    private val binding: ItemChatMessageVoiceRightBinding,
    private val mChatMessageListeners: ChatAdapterListeners
) : RecyclerView.ViewHolder(binding.root), VoiceMessageViewHolder,MessageHolder {

    private var mCurrentMessage: ChatMessageEntity? = null


    init {
        // 语音消息点击播放
        ClickUtils.setOnGlobalDebounceClickListener(binding.llVoiceContainer){
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageClickListener?.invoke(message,binding.llVoiceContainer)
            }
        }
        // 语音消息长按
        binding.llVoiceContainer.setOnLongClickListener{
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnMessageLongClickListener?.invoke(message,binding.llVoiceContainer) ?: false
            } ?: false
        }

        // 头像
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar){
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnAvatarClickListener?.invoke(message,binding.ivAvatar)
            }
        }
        
        // 重发按钮点击
        ClickUtils.setOnGlobalDebounceClickListener(binding.ivResend){
            mCurrentMessage?.let { message ->
                mChatMessageListeners.mOnResendClickListener?.invoke(message)
            }
        }
    }

    override fun bind(message: ChatMessageEntity) {
        mCurrentMessage = message
        updateStatus(message.status)
        // 加载头像
        GlideUtils.load(
            view =binding.ivAvatar,
            url = message.senderAvatar,
            placeholder = R.drawable.placeholder,
            error = R.drawable.placeholder,
            isCircle = true
        )
        
        // 显示语音时长
        val duration = message.mediaDuration / 1000L
        binding.tvDuration.text = if (duration > 0) "$duration\"" else "1\""

    }

    override fun updateStatus(status: MessageStatus) {
        // 根据消息状态显示状态图标
        when (status) {
            MessageStatus.SENDING -> {
                binding.progressSending.visibility = View.VISIBLE
                binding.ivStatus.visibility = View.GONE
                binding.ivResend.visibility = View.GONE
            }
            MessageStatus.FAILED -> {
                binding.progressSending.visibility = View.GONE
                binding.ivStatus.visibility = View.GONE
                binding.ivResend.visibility = View.VISIBLE
            }
            else -> {
                binding.progressSending.visibility = View.GONE
                binding.ivStatus.visibility = View.GONE
                binding.ivResend.visibility = View.GONE
            }
        }
    }

    override fun updateEntity(messageEntity: ChatMessageEntity) {
        this.mCurrentMessage = messageEntity
    }

    override fun startPlayAnimation() {
        // 实现语音播放动画 - 切换到播放中的图标
        CustomUtils.playSvga(binding.ivVoice, "voice_right.svga")
    }

    override fun stopPlayAnimation() {
        // 停止语音播放动画 - 切换回静态图标
        binding.ivVoice.stopAnimation()
    }

} 