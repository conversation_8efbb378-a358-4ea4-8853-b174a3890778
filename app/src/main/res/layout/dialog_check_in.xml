<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 主要弹窗内容 -->
    <FrameLayout
        android:id="@+id/check_in_content_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="23dp"
        android:background="@drawable/bg_dialog_rounded_check_in"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 关闭按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btn_close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="top|end"
            android:layout_marginEnd="13dp"
            android:layout_marginTop="15dp"
            android:scaleType="center"
            android:src="@drawable/promotion_dialog_cancel" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="20dp"
            android:paddingTop="80dp"
            android:paddingBottom="24dp">

            <!-- 标题 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:lineHeight="24dp"
                android:text="CHECK-IN FOR 7 DAYS\nCONTINUOUSLY"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- 签到日期网格 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical">

                <!-- 第一行：Day 1-4 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <LinearLayout
                        android:id="@+id/day1_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_check_in_day"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Day 1"
                            android:textColor="#666666"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day1_gift"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="8dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/coin" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="+60"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day1_check"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="4dp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/day2_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_check_in_day"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Day 2"
                            android:textColor="#666666"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day2_gift"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="8dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/coin" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="+60"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day2_check"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="4dp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/day3_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_check_in_day"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Day 3"
                            android:textColor="#666666"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day3_gift"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="8dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/coin" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="+60"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day3_check"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="4dp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/day4_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_check_in_day"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Day 4"
                            android:textColor="#666666"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day4_gift"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="8dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/coin" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="+60"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day4_check"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="4dp"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 第二行：Day 5-7 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:orientation="horizontal"
                    android:weightSum="3">

                    <LinearLayout
                        android:id="@+id/day5_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_check_in_day"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Day 5"
                            android:textColor="#666666"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day5_gift"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="8dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/coin" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="+60"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day5_check"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="4dp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/day6_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_check_in_day"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Day 6"
                            android:textColor="#666666"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day6_gift"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_marginTop="8dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/coin" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="+60"
                            android:textColor="#333333"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day6_check"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="4dp"
                            android:visibility="gone" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/day7_layout"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:background="@drawable/bg_check_in_special_day"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="8dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Day 7"
                            android:textColor="#666666"
                            android:textSize="12sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day7_gift"
                            android:layout_width="50dp"
                            android:layout_height="50dp"
                            android:layout_marginTop="8dp"
                            android:scaleType="centerInside"
                            android:src="@drawable/coin" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="+2000"
                            android:textColor="#FF6600"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/day7_check"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginTop="4dp"
                            android:visibility="gone" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- 底部提示文本 -->
            <TextView
                android:id="@+id/tv_check_in_tip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/bg_btn_rounded_blue"
                android:gravity="center"
                android:padding="12dp"
                android:text="See you tomorrow"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold"
                android:visibility="gone" />

            <!-- 签到按钮 -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_check_in"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="20dp"
                android:background="@drawable/bg_btn_rounded_black"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Check in"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:textStyle="bold" />
            </com.score.callmetest.ui.widget.AlphaLinearLayout>

            <!-- VIP特权按钮 -->
            <LinearLayout
                android:id="@+id/vip_privilege_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:background="@drawable/bg_btn_rounded_blue"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="12dp">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/customer_service_white" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Become VIP to unlock 8 privileges"
                    android:textColor="#000000"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_btn_rounded_black"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="6dp"
                    android:text="GO"
                    android:textColor="#FFFFFF"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </FrameLayout>

    <!-- 背景装饰 -->
    <ImageView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="-10dp"
        android:scaleType="centerCrop"
        android:src="@drawable/bg_check_in_top"
        app:layout_constraintBottom_toTopOf="@id/check_in_content_container"
        app:layout_constraintEnd_toEndOf="@id/check_in_content_container"
        app:layout_constraintStart_toStartOf="@id/check_in_content_container" />

</androidx.constraintlayout.widget.ConstraintLayout>
